import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps, useFocusEffect } from "@react-navigation/native";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import * as Location from 'expo-location';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from "react";
import { Alert, BackHandler, Image, Linking, Pressable, StyleSheet, View } from 'react-native';
import MapView, { MapPressEvent, Region } from 'react-native-maps';
import { IconButton, Searchbar, useTheme } from "react-native-paper";
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AddMarkerFAB from '../../components/AddMarkerFab';
import BasicInfoDialog from '../../components/BasicInfoDialog';
import ClusteredMapView from '../../components/ClusteredMapView';
import confirmationDialog from "../../components/ConfirmationDialog";
import CurrentLocationFAB from '../../components/CurrentLocationFAB';
import LoadingMapIndicator from '../../components/LoadingMapIndicator';
import MapLegend from '../../components/MapLegend';
import PharmacyResetDialog from '../../components/PharmacyResetDialog';
import PlaceCard from "../../components/PlaceCard";
import ResetExistingPharmaciesMarkersFAB from '../../components/ResetExistingPharmaciesMarkersFAB';
import SearchRateLimitReachedAlertDialog from '../../components/SearchRateLimitReachedAlertDialog';
import SearchThisAreaFAB from "../../components/SearchThisAreaFAB";
import SignUpPromptDialog from '../../components/SignUpPromptDialog';
import SubscriptionDialog from '../../components/SubscriptionDialog';
import useMapClusters from '../../hooks/useMapClusters';
import { PlaceType } from "../../models/constants";
import { Geometry } from "../../models/geometry";
import { CustomPlace, Place } from "../../models/place";
import { ApiService } from "../../services/api/apiService";
import { RectangleArea } from '../../services/api/models/pharmaciesSearchRequest';
import { authSelectors } from '../../services/authService';
import useGlobalStore, { GlobalStore } from '../../services/globalState';
import { apiServiceContext, translationServiceContext, whatsappServiceContext } from "../../services/provider";
import { TranslationService } from '../../services/translationService';
import { WhatsappService } from '../../services/whatsappService';
import { CIRCLE_DIAMETER_IN_METERS, DEFAULT_ZOOM_LEVEL, DELETE_SAVED_PLACE_OPERATION_SUFFIX, WHATSAPP_HELP_MSG } from '../../utils/constants';
import { useProtectedAction } from '../../utils/hooks/useProtectedAction';
import { usePurchaseFlow } from '../../utils/hooks/usePurchaseFlow';
import { getForegroundPermissionStatus, requestForegroundPermission } from '../../utils/locationPermissions';
import { getRectangleArea, isPharmacyOrChainage } from "../../utils/others";
import { ApiError } from '../../services/api/models/apiError';

type HomeScreenProps = CompositeScreenProps<
    BottomTabScreenProps<HomeStackParamList, 'Home'>,
    NativeStackScreenProps<RootStackParamList>
>;

const userPharmaciesSelector = {
    userPharmacies: (state: GlobalStore) => {
        const userUid: string | undefined = state.firebaseUser?.uid;
        return userUid ? (state.pharmacies[userUid] ?? []) : [];
    }
};

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation, route }) => {

    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const whatsappService: WhatsappService = useContext<WhatsappService>(whatsappServiceContext);

    const theme = useTheme();

    const { userUid, isUserAnonymous } = useGlobalStore(authSelectors.authenticatedUser);
    const { geometry, placeId, placeName, operation } = route.params || {};

    const searchResultPlaceId = operation === 'SEARCH' ? placeId : undefined;
    const searchResultPlaceName = operation === 'SEARCH' ? placeName : undefined;
    const searchResultGeometry = operation === 'SEARCH' ? geometry : undefined;

    const isUserSubscribed = useGlobalStore((store) => store.isUserSubscribed);

    const savedPlaces: ReadonlyArray<CustomPlace> = useGlobalStore((store) => store.savedPlaces);
    const pharmacies: ReadonlyArray<Place> = useGlobalStore(userPharmaciesSelector.userPharmacies);
    const currentRegion: Region = useGlobalStore((store) => store.currentRegion);

    const setCurrentRegion = useGlobalStore((store) => store.setCurrentRegion);
    const addPharmacy = useGlobalStore((store) => store.addPharmacy);
    const deletePharmacy = useGlobalStore((store) => store.deletePharmacy);
    const addPharmacies = useGlobalStore((store) => store.addPharmacies);
    const clearPharmacies = useGlobalStore((store) => store.clearPharmacies);
    const addSavedPlace = useGlobalStore((store) => store.addSavedPlace);
    const deleteSavedPlacesByPlaceTypeAndTag = useGlobalStore((store) => store.deleteHiddenExistingPharmacies);
    const deleteSavedPlace = useGlobalStore((store) => store.deleteSavedPlace);

    const hasSeenZoomHintDialog: boolean | null = useGlobalStore((store) => store.hasSeenZoomHintDialog);
    const setHasSeenZoomHintDialog = useGlobalStore((store) => store.setHasSeenZoomHintDialog);
    const [zoomHintDialogVisible, setZoomHintDialogVisible] = useState<boolean>(false);

    const startOperation = useGlobalStore((store) => store.startOperation);
    const completeOperation = useGlobalStore((store) => store.completeOperation);
    const showSnackbar = useGlobalStore((state) => state.showSnackbar);

    const [loadingPlaces, setLoadingPlaces] = useState<boolean>(false);
    const [selectedPlace, setSelectedPlace] = useState<Place | CustomPlace | undefined>();
    const [placeCardVisible, setPlaceCardVisible] = useState<boolean>(false);

    const [searchText, setSearchText] = useState<string>("");

    const [typeOfMarkerToBeAdded, setTypeOfMarkerToBeAdded] = useState<PlaceType | undefined>(undefined);
    const [searchResultPlace, setSearchResultPlace] = useState<Place | undefined>(undefined);

    const [pharmacyResetDialogVisible, setPharmacyResetDialogVisible] = useState<boolean>(false);

    const [subscriptionAlertDialog, setSubscriptionAlertDialog] = useState<'TRIAL_PERIOD_ENDED' | 'SUBSCRIPTION_ENDED' | null>(null);

    const [deletingSavedPlace, setDeletingSavedPlace] = useState<boolean>(false);
    const [searchRateLimitReachedDialog, setSearchRateLimitReachedDialog] = useState<{
        visible: boolean,
        currentTime?: Date,
        searchLimitPerDayForSubscribedUsers?: number,
        searchLimitPerDayForNonSubscribedUsers?: number,
        searchLimitPerDayForGuestUsers?: number
    }>({ visible: false });
    const [mapLoaded, setMapLoaded] = useState<boolean>(false);
    const mapRef: React.RefObject<MapView> = React.useRef<MapView>(null);

    const [animatingToRegion, setAnimatingToRegion] = useState<boolean>(false);

    const resetExistingPharmaciesButtonVisible: boolean = (pharmacies && pharmacies.length > 0) || (savedPlaces && savedPlaces.filter(savedPlace => PlaceType.EXISTING_PHARMACY === savedPlace.type).length > 0);
    const [addPlaceFABVisible, setAddPlaceFABVisible] = useState<boolean>(true);
    const [searchButtonVisible, setSearchButtonVisible] = useState<boolean>(true);

    const [locationPermissionGranted, setLocationPermissionGranted] = useState<boolean>(false);
    const [lookingForCurrentLocation, setLookingForCurrentLocation] = useState<boolean>(false);

    const places: ReadonlyArray<Place | CustomPlace> = useMemo(() => {
        return searchResultPlace ?
            [...savedPlaces, ...pharmacies, searchResultPlace] :
            [...savedPlaces, ...pharmacies];
    }, [savedPlaces, pharmacies, searchResultPlace]);

    const insets = useSafeAreaInsets(); // For safe area handling

    const { clusters, updateClusters } = useMapClusters(places, mapRef);

    useEffect(() => {
        // used to remove place card after saved place deletion from saved place details screen 😎
        setPlaceCardVisible(
            selectedPlace &&
                ((isPharmacyOrChainage(selectedPlace) && _.some(savedPlaces, { placeId: selectedPlace.placeId }))
                    || PlaceType.EXISTING_PHARMACY === selectedPlace.type)
                ? true
                : false
        );
    }, [savedPlaces, selectedPlace]);

    useEffect(() => {
        if (mapLoaded && placeId) {
            const foundPlace = savedPlaces.find(savedPlace => placeId === savedPlace.placeId);
            if (foundPlace) {
                setSelectedPlace(foundPlace);
            } else {
                setSelectedPlace(searchResultPlace);
            }
        }
    }, [mapLoaded, placeId]);

    useEffect(() => {
        if (mapLoaded && searchResultPlaceName && searchResultPlaceId && searchResultGeometry) {
            setSearchText(searchResultPlaceName);
            const pharmacySearchResult = pharmacies.find(pharmacy => searchResultPlaceId === pharmacy.placeId);

            if (pharmacySearchResult) {
                setSelectedPlace(pharmacySearchResult);
            } else {
                const newSearchResult: Place = {
                    geometry: searchResultGeometry,
                    name: searchResultPlaceName,
                    placeId: searchResultPlaceId,
                    type: PlaceType.SEARCH_RESULT,
                };
                setSearchResultPlace(newSearchResult);
            }
        }
    }, [mapLoaded, searchResultPlaceId, searchResultPlaceName, searchResultGeometry, pharmacies]);

    useEffect(() => {
        if (searchResultPlace) {
            setSelectedPlace(searchResultPlace);
        }
    }, [searchResultPlace]);

    useEffect(() => {
        if (mapLoaded && selectedPlace && !animatingToRegion) {
            setPlaceCardVisible(true);
        }
    }, [mapLoaded, animatingToRegion, selectedPlace]);

    useEffect(() => {
        if (mapLoaded && mapRef.current && geometry) {
            setPlaceCardVisible(false);
            setAnimatingToRegion(true);
            mapRef?.current?.animateToRegion({
                latitude: geometry.lat,
                longitude: geometry.lng,
                ...DEFAULT_ZOOM_LEVEL
            }, 1000);
        }
    }, [mapLoaded, geometry]);

    useEffect(() => {
        const backAction = () => {
            if (typeOfMarkerToBeAdded) {
                // Cancel marker addition
                setTypeOfMarkerToBeAdded(undefined);
                return true; // Prevent default back action
            }
            return false; // Allow default back action
        };

        const backHandler = BackHandler.addEventListener("hardwareBackPress", backAction);

        return () => backHandler.remove(); // Clean up the listener
    }, [typeOfMarkerToBeAdded]);

    // Add this useEffect to update clusters when the map is loaded
    useEffect(() => {
        if (mapLoaded && mapRef.current) {
            updateClusters();
        }
    }, [mapLoaded]);

    useFocusEffect(
        React.useCallback(() => {
            // Set FAB visible when screen is focused
            setAddPlaceFABVisible(true);
            return () => {
                // Hide FAB when screen is blurred
                setAddPlaceFABVisible(false);
                dismissSearchResult();
            };
        }, [])
    );

    const handleLocationPress = async () => {
        const foregroundStatus = await getForegroundPermissionStatus();
        setLookingForCurrentLocation(true);
        if (foregroundStatus !== Location.PermissionStatus.GRANTED) {
            const newStatus = await requestForegroundPermission();
            if (newStatus !== Location.PermissionStatus.GRANTED) {
                setLookingForCurrentLocation(false);
                return;
            }
        }

        setLocationPermissionGranted(true); // Permission granted

        try {
            const currentLocation = await Location.getCurrentPositionAsync({});
            setCurrentRegion({
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
                ...DEFAULT_ZOOM_LEVEL
            });
            mapRef?.current?.animateToRegion({
                latitude: currentLocation.coords.latitude,
                longitude: currentLocation.coords.longitude,
                latitudeDelta: 0.005,
                longitudeDelta: 0.005,
            });
        } catch (error) {
            Alert.alert(
                translationService.translate("LOCATION_ERROR_TITLE"),
                translationService.translate("LOCATION_ERROR_MESSAGE"),
                [{ text: translationService.translate("OK"), style: "cancel" }]
            );
        } finally {
            setLookingForCurrentLocation(false);
        }
    };

    const navigateToAddModal = (geometry: Geometry, placeType: PlaceType) => {
        navigation.navigate({
            name: "SavedPlaceAdd",
            params: {
                geometry,
                placeType
            }
        });
    };

    const startDeletingSavedPlaceOperation = (operationId: string, placeType: PlaceType) => {
        startOperation(
            operationId,
            _.cond([
                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_DELETION_IN_PROGRESS')],
                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_DELETION_IN_PROGRESS')],
                [_.stubTrue, () => translationService.translate('DELETION_IN_PROGRESS')]
            ])(placeType),
            'info'
        );
    };

    const completeDeletingSavedPlaceOperationSuccessfully = (operationId: string, placeType: PlaceType) => {
        completeOperation(
            operationId,
            _.cond([
                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_DELETED_SUCCESS')],
                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_DELETED_SUCCESS')],
                [_.stubTrue, () => translationService.translate('DELETION_SUCCESS')]
            ])(placeType),
            'success'
        );
    };

    const completeDeletingSavedPlaceOperationBadly = (operationId: string, placeType: PlaceType) => {
        completeOperation(
            operationId,
            _.cond([
                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_DELETION_FAILED')],
                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_DELETION_FAILED')],
                [_.stubTrue, () => translationService.translate('DELETION_FAILED')]
            ])(placeType),
            'error'
        );
    };

    const onPlaceBannerInfoDelete = async () => {
        if (selectedPlace && 'id' in selectedPlace) {
            const operationId: string = `${DELETE_SAVED_PLACE_OPERATION_SUFFIX}_${selectedPlace.type}_${selectedPlace.placeId}`;
            const placeType: PlaceType = selectedPlace.type;
            confirmationDialog({
                title: translationService.translate("DELETION_TITLE"),
                description: translationService.translate("DELETION_DESCRIPTION"),
                primaryActionLabel: translationService.translate("DELETE"),
                onPrimaryAction: async () => {
                    setDeletingSavedPlace(true);
                    startDeletingSavedPlaceOperation(operationId, selectedPlace.type);
                    try {
                        await apiService.deleteUserSavedPlace(userUid, selectedPlace.placeId);
                        deleteSavedPlace(selectedPlace.placeId);
                        setPlaceCardVisible(false);
                        completeDeletingSavedPlaceOperationSuccessfully(operationId, placeType);
                    } catch (error) {
                        completeDeletingSavedPlaceOperationBadly(operationId, placeType);
                    } finally {
                        setDeletingSavedPlace(false);
                    }
                },
                cancelActionLabel: translationService.translate("CANCEL"),
                onCancel: () => {
                }
            });
        } else {
            throw new Error("selectedPlace is possibly undefined or not of type CustomPlace");
        }
    };

    const dismissPlaceCard = () => {
        if (searchResultPlaceId === selectedPlace?.placeId) {
            dismissSearchResult();
        }
        setPlaceCardVisible(false);
        setSelectedPlace(undefined);
    };

    const onShowPharmacyCirclePress = async () => {
        if (!selectedPlace) {
            console.error("SelectedPlace is not defined.");
            return Promise.resolve();
        }
        try {
            const deletedSavedPlace = await apiService.deleteUserSavedPlace(userUid, selectedPlace.placeId);
            dismissPlaceCard();
            deleteSavedPlace(deletedSavedPlace.placeId);
            addPharmacy(deletedSavedPlace);
            showSnackbar(translationService.translate('PERIMETER_SHOWN_SUCCESS'), 'success');
        } catch (error) {
            showSnackbar(translationService.translate('PERIMETER_SHOW_ERROR'), 'error');
        }
    };

    const { protectAction, signUpDialogState, closeSignUpDialog } = useProtectedAction();

    const onHidePharmacyCirclePress = async () => {
        if (!selectedPlace) {
            console.error("SelectedPlace is not defined");
            return Promise.resolve();
        }
        try {
            const savedPlace = await apiService.addSavedPlaceTag(userUid, selectedPlace.placeId, {
                tag: 'HIDDEN',
                savedPlaceType: PlaceType.EXISTING_PHARMACY
            });
            deletePharmacy(savedPlace.placeId);
            addSavedPlace(savedPlace);
            dismissPlaceCard();
            showSnackbar(translationService.translate('PERIMETER_HIDDEN_SUCCESS'), 'success');
        } catch (error) {
            console.error("Failed to hide the perimeter:", error);
            showSnackbar(translationService.translate('PERIMETER_HIDE_ERROR'), 'error');
        }
    };

    const protectedOnHidePharmacyCirclePress = async () => {
        protectAction(
            onHidePharmacyCirclePress,
            "HIDE_PERIMETER",
            "Personnalisation limitée",
            "eye-off",
            "Créez un compte gratuit pour personnaliser l'affichage des pharmacies et sauvegarder vos préférences."
        )();
    };

    const protectedOnShowPharmacyCirclePress = async () => {
        protectAction(
            onShowPharmacyCirclePress,
            "SHOW_PERIMETER",
            "Personnalisation limitée",
            "eye",
            "Créez un compte gratuit pour personnaliser l'affichage des pharmacies et sauvegarder vos préférences."
        )();
    };

    const protectedOnPlaceBannerInfoDelete = async () => {
        protectAction(
            onPlaceBannerInfoDelete,
            "DELETE_SAVED_PLACE",
            "Suppression limitée",
            "delete",
            "Créez un compte gratuit pour gérer vos lieux sauvegardés et y accéder depuis tous vos appareils."
        )();
    };

    const generateBlockedRequestReasonMessage = (apiError: ApiError<{ reason?: string }>): string => {
        const reason: string | undefined | null = apiError.details?.reason;
        if (reason) {
            return `\n${translationService.translate('REQUEST_BLOCKED_' + reason)}`;
        } else {
            return '';
        }
    };

    // Handle 403 Forbidden errors (subscription/authorization issues)
    const handleForbiddenError = async (response: Response) => {
        const apiError = await response.json();
        if ("SUBSCRIPTION_ENDED" === apiError.code) {
            setSubscriptionAlertDialog("SUBSCRIPTION_ENDED");
        } else if ("TRIAL_PERIOD_ENDED" === apiError.code) {
            setSubscriptionAlertDialog("TRIAL_PERIOD_ENDED");
        } else if ("REQUEST_BLOCKED" === apiError.code) {
            Alert.alert(
                'Non autorisé',
                `Vous n'êtes pas autorisé à effectuer cette action.${generateBlockedRequestReasonMessage(apiError)}`,
                [
                    {
                        text: translationService.translate("CONTACT_SUPPORT"),
                        onPress: () => {
                            Linking.openURL(whatsappService.generateMsgLink(WHATSAPP_HELP_MSG));
                        },
                    },
                    {
                        text: translationService.translate("OK"),
                        onPress: () => {
                            // action on OK
                        },
                    },
                ]
            );
        } else {
            console.error("Can't get list of pharmacies for the given geometry.");
        }
    };

    // Handle 429 Rate Limit errors
    const handleRateLimitError = async (response: Response) => {
        const apiError = await response.json() as ApiError<{
            searchLimitPerDayForNonSubscribedUsers: number,
            searchLimitPerDayForSubscribedUsers: number,
            searchLimitPerDayForGuestUsers: number
        }>

        if ("QUERY_LIMIT_REACHED" === apiError.code) {
            if (isUserAnonymous) {
                protectAction(
                    () => { },
                    "SEARCH_LIMIT_REACHED",
                    translationService.translate("QUERY_LIMIT_REACHED_DIALOG_TITLE"),
                    "magnify-close",
                    "Créez un compte gratuit pour débloquer plus de recherches quotidiennes."
                )();
            } else {
                setSearchRateLimitReachedDialog({
                    visible: true,
                    currentTime: new Date(),
                    searchLimitPerDayForNonSubscribedUsers: apiError.details?.searchLimitPerDayForNonSubscribedUsers,
                    searchLimitPerDayForSubscribedUsers: apiError.details?.searchLimitPerDayForSubscribedUsers,
                    searchLimitPerDayForGuestUsers: apiError.details?.searchLimitPerDayForGuestUsers
                });
            }
        } else {
            console.error("Can't get list of pharmacies for the given geometry.");
        }
    };

    // Handle successful pharmacy search response
    const handleSuccessfulSearch = async (response: Response) => {
        const pharmaciesList: ReadonlyArray<Place> = await response.json();
        addPharmacies(pharmaciesList);

        if (pharmaciesList.length === 0) {
            showSnackbar(translationService.translate('NO_PHARMACIES_FOUND'), 'info');
        }

        setSearchButtonVisible(false);

        if (hasSeenZoomHintDialog === false) {
            setZoomHintDialogVisible(true);
            setHasSeenZoomHintDialog(true);
        }
    };

    // Main search pharmacies function - now much cleaner and focused
    const searchPharmacies = async (rectangleArea: RectangleArea) => {
        dismissSearchResult();
        setLoadingPlaces(true);
        try {
            const response = await apiService.searchPharmacies({ rectangleArea });
            if (403 === response.status) {
                await handleForbiddenError(response);
            } else if (429 === response.status) {
                await handleRateLimitError(response);
            } else {
                await handleSuccessfulSearch(response);
            }
        } catch (error) {
            console.error("Error searching pharmacies:", error);
            showSnackbar(translationService.translate('SEARCH_ERROR'), 'error');
        } finally {
            setLoadingPlaces(false);
        }
    };

    const onSearchThisAreaPress = () => {
        dismissSearchResult();
        dismissPlaceCard();
        const rectangleArea: RectangleArea = getRectangleArea(currentRegion);
        searchPharmacies(rectangleArea);
    };

    const navigateToSearchScreen = () => {
        dismissSearchResult();
        dismissPlaceCard();
        navigation.navigate('Search');
    };

    const onMarkerPress = (place: Place) => {
        if (PlaceType.SEARCH_RESULT !== place.type) {
            dismissSearchResult();
        }
        setSelectedPlace(place);
        setAnimatingToRegion(true);
        mapRef?.current?.getCamera()
            .then((camera) => {
                mapRef?.current?.animateCamera({
                    ...camera,
                    center: {
                        latitude: place.geometry.lat,
                        longitude: place.geometry.lng,
                    }
                });
            })
            .catch((error) => {
                console.error("Error getting camera properties:", error)
            });
    };

    const resetPharmacies = (removeExistingPharmaciesWithHiddenCircles: boolean): Promise<void> => {
        return new Promise((resolve, reject) => {
            const resetPharmacies = () => {
                dismissSearchResult();
                clearPharmacies(userUid);
                if (removeExistingPharmaciesWithHiddenCircles) {
                    deleteSavedPlacesByPlaceTypeAndTag();
                }
                setPharmacyResetDialogVisible(false); // Close the dialog
                setSearchButtonVisible(true);
                resolve(); // Resolve the promise once the reset is complete
            };

            if (removeExistingPharmaciesWithHiddenCircles) {
                apiService.deleteHiddenExistingPharmacies(userUid)
                    .then(() => {
                        resetPharmacies();
                    })
                    .catch((error) => {
                        console.error("Failed to remove all tags:", error);
                        reject(error); // Reject the promise in case of an error
                    });
            } else {
                resetPharmacies(); // No need to remove tags, just reset the pharmacies
            }
        });
    };

    const dismissSearchResult = () => {
        navigation.setParams({
            placeId: undefined,
            placeName: undefined,
            geometry: undefined,
        });
        setSelectedPlace(undefined);
        setSearchResultPlace(undefined);
        setPlaceCardVisible(false);
        setSearchText("");
    };

    const dismissSearchRateLimitReachedDialog = () => {
        setSearchRateLimitReachedDialog({
            ...searchRateLimitReachedDialog,
            visible: false,
        });
    };

    const { startPurchaseFlow } = usePurchaseFlow();

    const onAddChainagePress = () => {
        setTypeOfMarkerToBeAdded(PlaceType.CHAINAGE);
    };

    const onAddPharmacyPress = () => {
        setTypeOfMarkerToBeAdded(PlaceType.PHARMACY);
    };

    const protectedOnAddChainagePress = () => {
        protectAction(
            onAddChainagePress,
            "ADD_CHAINAGE",
            "Ajout de chaînage limité",
            "shape-circle-plus",
            "Créez un compte gratuit pour sauvegarder vos chaînages et y accéder depuis tous vos appareils."
        )();
    };

    const protectedOnAddPharmacyPress = () => {
        protectAction(
            onAddPharmacyPress,
            "ADD_PHARMACY",
            "Ajout de pharmacie limité",
            "plus-circle-outline",
            "Créez un compte gratuit pour sauvegarder vos pharmacies et y accéder depuis tous vos appareils."
        )();
    };

    const addMarkerButtonVisible: boolean = (mapLoaded && currentRegion && !typeOfMarkerToBeAdded && addPlaceFABVisible && !placeCardVisible) ?? false

    return (
        <View style={styles.container}>
            <SignUpPromptDialog
                visible={signUpDialogState.visible}
                onDismiss={closeSignUpDialog}
                featureName={signUpDialogState.featureName}
                title={signUpDialogState.title}
                icon={signUpDialogState.icon}
                description={signUpDialogState.description}
            />
            <SearchRateLimitReachedAlertDialog
                subscribed={isUserSubscribed(searchRateLimitReachedDialog.currentTime ?? new Date())}
                visible={searchRateLimitReachedDialog.visible}
                limits={{
                    searchLimitPerDayForNonSubscribedUsers: searchRateLimitReachedDialog.searchLimitPerDayForNonSubscribedUsers,
                    searchLimitPerDayForSubscribedUsers: searchRateLimitReachedDialog.searchLimitPerDayForSubscribedUsers
                }}
                onDismiss={() => {
                    dismissSearchRateLimitReachedDialog();
                }}
                subscribeButtonIcon={'credit-card-outline'}
                onSubscribe={() => {
                    dismissSearchRateLimitReachedDialog();
                    startPurchaseFlow(userUid);
                }}
            />
            <SubscriptionDialog
                visible={subscriptionAlertDialog !== null}
                causeOfSubscriptionRequest={subscriptionAlertDialog}
                onDismiss={() => setSubscriptionAlertDialog(null)}
                subscribeButtonIcon={'credit-card-outline'}
                onSubscribe={() => {
                    startPurchaseFlow(userUid);
                }}
            />
            <BasicInfoDialog
                visible={zoomHintDialogVisible}
                icon="map-search"
                title={translationService.translate("ZOOM_TIP_DIALOG_TITLE")}
                content={translationService.translate("ZOOM_TIP_DIALOG_CONTENT")}
                mainActionButtonLabel={translationService.translate("CONTINUE")}
                onDismiss={() => setZoomHintDialogVisible(false)}
                onMainAction={() => setZoomHintDialogVisible(false)}
            />
            <PharmacyResetDialog
                visible={pharmacyResetDialogVisible}
                disableRemovalOfExistingPharmaciesWithHiddenCircles={isUserAnonymous}
                onHide={() => setPharmacyResetDialogVisible(false)}
                onConfirm={(removeExistingPharmaciesWithHiddenCircles) => resetPharmacies(removeExistingPharmaciesWithHiddenCircles)}
            />
            <View style={styles.container}>
                <View>
                    <Pressable onPress={navigateToSearchScreen}>
                        <Searchbar
                            mode="view"
                            placeholder={translationService.translate("SEARCH_BAR_PLACEHOLDER")}
                            numberOfLines={1}
                            editable={false}
                            onPressIn={navigateToSearchScreen}
                            value={searchText}
                            onChangeText={(query: string) => setSearchText(query)}
                            onClearIconPress={() => {
                                dismissSearchResult();
                            }}
                            showDivider={false}
                        />
                    </Pressable>
                </View>
                <View style={styles.container}>
                    <SearchThisAreaFAB
                        visible={mapLoaded && searchButtonVisible && !typeOfMarkerToBeAdded}
                        containerStyle={styles.searchButton}
                        loading={loadingPlaces}
                        onPress={onSearchThisAreaPress}
                    />
                    <ClusteredMapView
                        ref={mapRef}
                        style={styles.container}
                        showUserLocation={locationPermissionGranted}
                        initialRegion={currentRegion}
                        selectedPlace={selectedPlace}
                        onMapLoaded={() => setMapLoaded(true)}
                        addingMarker={!!typeOfMarkerToBeAdded}
                        places={places}
                        clusters={clusters}
                        onClusterMarkerPress={({ latitude, longitude }) => {
                            mapRef.current?.animateToRegion({
                                latitude,
                                longitude,
                                latitudeDelta: currentRegion.latitudeDelta / 2,
                                longitudeDelta: currentRegion.longitudeDelta / 2,
                            }, 1000);
                        }}
                        onMarkerPress={onMarkerPress}
                        onPress={(event: MapPressEvent) => {
                            if (event.nativeEvent.action !== 'marker-press') {
                                if (placeCardVisible) {
                                    dismissPlaceCard();
                                }
                            }
                        }}
                        onRegionChangeComplete={(newRegion, details) => {
                            setAnimatingToRegion(false);
                            setSearchButtonVisible(true);
                            setCurrentRegion(newRegion);
                            updateClusters();
                        }}
                    />
                    {/* FAB Container */}
                    <View style={[styles.fabContainer, { paddingBottom: insets.bottom + 16 }]}>
                        <AddMarkerFAB
                            visible={addMarkerButtonVisible}
                            onAddChainagePress={protectedOnAddChainagePress}
                            onAddPharmacyPress={protectedOnAddPharmacyPress}
                        />
                        <ResetExistingPharmaciesMarkersFAB
                            visible={mapLoaded && resetExistingPharmaciesButtonVisible && !typeOfMarkerToBeAdded && !placeCardVisible}
                            onPress={() => setPharmacyResetDialogVisible(true)}
                        />
                        <CurrentLocationFAB
                            loading={lookingForCurrentLocation}
                            visible={mapLoaded && !typeOfMarkerToBeAdded && !placeCardVisible}
                            onPress={handleLocationPress}
                        />
                    </View>
                    {mapLoaded && currentRegion && typeOfMarkerToBeAdded ? (
                        <>
                            <View style={styles.markerFixed}>
                                {PlaceType.PHARMACY === typeOfMarkerToBeAdded ? (
                                    <Image style={styles.marker} source={require('../../assets/images/pins/orange-white-border-pin.png')} />
                                ) : (
                                    <Image style={styles.marker} source={require('../../assets/images/pins/green-white-border-pin.png')} />
                                )}
                            </View>
                            <View style={styles.markerActions}>
                                <IconButton
                                    icon="close"
                                    style={{ marginHorizontal: 8, borderWidth: 4, borderColor: '#D32F2F' }} // Adjust spacing here
                                    containerColor='white'
                                    iconColor='#D32F2F'
                                    mode="contained"
                                    size={48}
                                    onPress={() => {
                                        setTypeOfMarkerToBeAdded(undefined);
                                    }}
                                />
                                <IconButton
                                    icon="check"
                                    style={{ marginHorizontal: 8, borderWidth: 4, borderColor: theme.colors.primary }} // Adjust spacing here
                                    containerColor='white'
                                    mode="contained"
                                    size={48}
                                    onPress={() => {
                                        navigateToAddModal({ lat: currentRegion?.latitude, lng: currentRegion?.longitude }, typeOfMarkerToBeAdded);
                                        setTypeOfMarkerToBeAdded(undefined);
                                    }}
                                />
                            </View>
                        </>
                    ) : undefined}
                    {!mapLoaded && <LoadingMapIndicator />}
                    {mapLoaded && !typeOfMarkerToBeAdded && (
                        <View style={styles.mapLegendContainer}>
                            <MapLegend circleDiameter={CIRCLE_DIAMETER_IN_METERS} />
                        </View>
                    )}
                    {mapLoaded && placeCardVisible && selectedPlace && (
                        <View style={styles.card}>
                            <PlaceCard
                                key={selectedPlace.placeId}
                                place={selectedPlace}
                                onDetailsPress={() => {
                                    navigation.navigate({
                                        name: "SavedPlaceDetails",
                                        params: {
                                            placeId: selectedPlace.placeId
                                        }
                                    });
                                }}
                                onDelete={protectedOnPlaceBannerInfoDelete}
                                deleting={deletingSavedPlace}
                                onDismiss={dismissPlaceCard}
                                onHidePharmacyPerimeterPress={protectedOnHidePharmacyCirclePress}
                                onShowPharmacyPerimeterPress={protectedOnShowPharmacyCirclePress}
                            />
                        </View>
                    )}
                </View>

            </View>

        </View >
    );
}

const MARKER_WIDTH = 26; // Width of the marker image in pixels
const MARKER_HEIGHT = 37; // Height of the marker image in pixels

const styles = StyleSheet.create({
    mapLegendContainer: {
        position: 'absolute',
        bottom: 36,
        left: 16,
    },
    markerFixed: {
        opacity: 0.7,
        position: 'absolute',
        top: '50%', // Vertically center the marker
        left: '50%', // Horizontally center the marker
        transform: [
            { translateX: -MARKER_WIDTH / 2 },
            { translateY: -MARKER_HEIGHT / 2 + 18 }, // Adjust the vertical offset as needed
        ],
        zIndex: 15,
    },
    markerActions: {
        position: 'absolute',
        bottom: 100, // Place the buttons below the marker
        left: 0, // Center horizontally
        right: 0, // Center horizontally
        flexDirection: 'row', // Arrange buttons horizontally
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 15,
        paddingHorizontal: 20
    },
    marker: {
        height: MARKER_HEIGHT, // Adjust size as needed
        width: MARKER_WIDTH, // Adjust size as needed
        resizeMode: 'contain', // Ensure proper scaling for the image
    },
    card: {
        position: 'absolute',
        left: 10,
        right: 10,
        bottom: 30,
        zIndex: 999999
    },
    searchButton: {
        position: 'absolute',
        top: 48,
        alignSelf: 'center',
        zIndex: 10,
        width: 'auto'
    },
    fabContainer: {
        position: 'absolute',
        right: 16,
        bottom: 16,
        flexDirection: 'column',
        alignItems: 'flex-end'
    },
    container: {
        flex: 1,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    separator: {
        marginVertical: 30,
        height: 1,
        width: '80%',
    }
});

export default HomeScreen;
