import { Alert } from 'react-native';
import { TranslationService } from './translationService';

export class NetworkErrorHandler {
    private translationService: TranslationService;

    constructor(translationService: TranslationService) {
        this.translationService = translationService;
    }

    public handleNetworkError = (error: any) => {
        console.warn('Network error detected:', error);
        
        Alert.alert(
            this.translationService.translate("NETWORK_ERROR"),
            this.translationService.translate("NETWORK_ERROR_DETAILS"),
            [
                {
                    text: this.translationService.translate("OK"),
                    style: "cancel"
                }
            ]
        );
    };
}
